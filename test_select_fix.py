#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试select功能修复的脚本
"""

import os
import tempfile
import shutil
from PIL import Image
from cell_cover.utils.image_splitter import split_image_into_four

def create_test_image(width=400, height=400):
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (width, height), color='white')
    
    # 在四个象限画不同颜色
    pixels = img.load()
    for x in range(width):
        for y in range(height):
            if x < width//2 and y < height//2:
                pixels[x, y] = (255, 0, 0)  # 红色 - u1
            elif x >= width//2 and y < height//2:
                pixels[x, y] = (0, 255, 0)  # 绿色 - u2
            elif x < width//2 and y >= height//2:
                pixels[x, y] = (0, 0, 255)  # 蓝色 - u3
            else:
                pixels[x, y] = (255, 255, 0)  # 黄色 - u4
    
    return img

def test_select_functionality():
    """测试select功能"""
    print("开始测试select功能修复...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建测试图片
        test_image_path = os.path.join(temp_dir, "test_image.png")
        test_img = create_test_image()
        test_img.save(test_image_path)
        print(f"创建测试图片: {test_image_path}")
        
        # 测试切割功能，选择u1和u3
        selected_parts = ['u1', 'u3']
        print(f"测试选择部分: {selected_parts}")
        
        output_paths, selected_paths = split_image_into_four(
            test_image_path, 
            temp_dir, 
            selected_parts
        )
        
        print("\n=== 测试结果 ===")
        print(f"所有切割文件 (应该在 {temp_dir}/images 目录下):")
        images_dir = os.path.join(temp_dir, "images")
        for path in output_paths:
            exists = os.path.exists(path)
            in_images_dir = path.startswith(images_dir)
            print(f"  - {path} (存在: {exists}, 在images目录: {in_images_dir})")
        
        print(f"\n选中的文件 (应该在 {temp_dir} 目录下):")
        for path in selected_paths:
            exists = os.path.exists(path)
            in_current_dir = os.path.dirname(path) == temp_dir
            print(f"  - {path} (存在: {exists}, 在当前目录: {in_current_dir})")
        
        # 验证目录结构
        print(f"\n=== 目录结构验证 ===")
        print(f"当前目录 ({temp_dir}) 内容:")
        for item in os.listdir(temp_dir):
            item_path = os.path.join(temp_dir, item)
            if os.path.isfile(item_path):
                print(f"  文件: {item}")
            else:
                print(f"  目录: {item}/")
        
        images_dir = os.path.join(temp_dir, "images")
        if os.path.exists(images_dir):
            print(f"\nimages目录 ({images_dir}) 内容:")
            for item in os.listdir(images_dir):
                print(f"  文件: {item}")
        
        # 验证逻辑
        print(f"\n=== 逻辑验证 ===")
        
        # 检查是否所有切割文件都在images目录
        all_in_images = all(path.startswith(images_dir) for path in output_paths)
        print(f"所有切割文件都在images目录: {all_in_images}")
        
        # 检查选中文件是否在当前目录
        all_selected_in_current = all(os.path.dirname(path) == temp_dir for path in selected_paths)
        print(f"所有选中文件都在当前目录: {all_selected_in_current}")
        
        # 检查选中文件数量
        expected_selected_count = len(selected_parts)
        actual_selected_count = len(selected_paths)
        print(f"选中文件数量正确: {expected_selected_count == actual_selected_count} (期望: {expected_selected_count}, 实际: {actual_selected_count})")
        
        # 检查总切割文件数量
        expected_total_count = 4  # 总是4个部分
        actual_total_count = len(output_paths)
        print(f"总切割文件数量正确: {expected_total_count == actual_total_count} (期望: {expected_total_count}, 实际: {actual_total_count})")
        
        # 总结
        success = (all_in_images and all_selected_in_current and 
                  expected_selected_count == actual_selected_count and
                  expected_total_count == actual_total_count)
        
        print(f"\n=== 测试结果 ===")
        if success:
            print("✅ 测试通过！select功能修复成功。")
            print("✅ 所有切割照片正确保存到images文件夹")
            print("✅ 选中照片正确复制到当前目录")
        else:
            print("❌ 测试失败！需要进一步检查。")
        
        return success

if __name__ == "__main__":
    test_select_functionality()
